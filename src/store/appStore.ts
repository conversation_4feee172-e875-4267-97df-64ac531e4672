import { create } from 'zustand';
import {
  DirectoryInfo,
  ScanDirectoryResponse,
  FilterConfiguration,
  ViewType,
  MusicSchema,
  Notification
} from '../types';

// Alias for backward compatibility
export type ScanResult = ScanDirectoryResponse;

interface AppState {
  // Directory selection
  selectedPath: string;
  setSelectedPath: (path: string) => void;

  // Scanning state
  isScanning: boolean;
  setIsScanning: (scanning: boolean) => void;
  scanResults: ScanResult | null;
  setScanResults: (results: ScanResult | null) => void;

  // Filtering
  filterConfig: FilterConfiguration;
  setFilterConfig: (config: FilterConfiguration) => void;
  updateFilterConfig: (updates: Partial<FilterConfiguration>) => void;

  // Schema generation
  generatedSchema: MusicSchema | null;
  setGeneratedSchema: (schema: MusicSchema | null) => void;

  // UI state
  currentView: ViewType;
  setCurrentView: (view: ViewType) => void;

  // Notifications
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;

  // Error handling (deprecated in favor of notifications)
  error: string | null;
  setError: (error: string | null) => void;
}

export const useAppStore = create<AppState>((set, get) => ({
  // Directory selection
  selectedPath: '',
  setSelectedPath: (path) => set({ selectedPath: path }),

  // Scanning state
  isScanning: false,
  setIsScanning: (scanning) => set({ isScanning: scanning }),
  scanResults: null,
  setScanResults: (results) => set({ scanResults: results }),

  // Filtering
  filterConfig: {
    extensions: {
      mode: 'include',
      extensions: ['.mp3', '.flac', '.wav', '.m4a', '.aac', '.ogg'],
    },
    patterns: {
      include_patterns: [],
      exclude_patterns: [],
      case_sensitive: false,
    },
    size: {
      enabled: false,
      unit: 'mb',
    },
    depth: {
      enabled: false,
      max_depth: 10,
    },
    regex: [],
  },
  setFilterConfig: (config) => set({ filterConfig: config }),
  updateFilterConfig: (updates) => set({
    filterConfig: { ...get().filterConfig, ...updates }
  }),

  // Schema generation
  generatedSchema: null,
  setGeneratedSchema: (schema) => set({ generatedSchema: schema }),

  // UI state
  currentView: 'directory',
  setCurrentView: (view) => set({ currentView: view }),

  // Notifications
  notifications: [],
  addNotification: (notification) => {
    const id = Date.now().toString();
    set({
      notifications: [...get().notifications, { ...notification, id }]
    });
  },
  removeNotification: (id) => set({
    notifications: get().notifications.filter(n => n.id !== id)
  }),
  clearNotifications: () => set({ notifications: [] }),

  // Error handling (deprecated in favor of notifications)
  error: null,
  setError: (error) => set({ error }),
}));
