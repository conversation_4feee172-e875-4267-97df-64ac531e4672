// Audio metadata types
export interface AudioMetadata {
  title?: string;
  artist?: string;
  album?: string;
  genre?: string;
  year?: number;
  track?: number;
  duration?: number; // in seconds
  bitrate?: number;
  sample_rate?: number;
  format: string;
}

export interface AudioFileInfo {
  path: string;
  filename: string;
  size: number;
  metadata?: AudioMetadata;
}

// Pattern analysis types
export interface PatternAnalysis {
  filename_patterns: string[];
  directory_patterns: string[];
  metadata_consistency: Record<string, number>; // field -> consistency score (0.0 to 1.0)
  genre_distribution: Record<string, number>;
  common_naming_conventions: string[];
}

// Schema configuration types
export interface ScanConfiguration {
  filters_applied: string[];
  scan_depth?: number;
  included_extensions: string[];
  excluded_paths: string[];
}

// Directory structure types
export interface DirectoryHierarchy {
  name: string;
  path: string;
  children: DirectoryHierarchy[];
  file_count: number;
  audio_file_count: number;
}

export interface OrganizationPatterns {
  genre_based_organization: boolean;
  artist_album_structure: boolean;
  year_based_subdivision: boolean;
  common_patterns: string[];
}

export interface DirectoryStructureSchema {
  hierarchy: DirectoryHierarchy;
  organization_patterns: OrganizationPatterns;
}

// File classification types
export interface AudioFormatInfo {
  count: number;
  avg_bitrate?: number;
  avg_sample_rate?: number;
  total_size_mb: number;
}

export interface NamingConventions {
  patterns: string[];
  consistency: number;
  common_separators: string[];
}

export interface FileClassificationSchema {
  audio_formats: Record<string, AudioFormatInfo>;
  naming_conventions: NamingConventions;
}

// Metadata analysis types
export interface FormatRule {
  pattern: string;
  examples: string[];
  description: string;
}

export interface MetadataAnalysisSchema {
  required_fields: string[];
  optional_fields: string[];
  empty_fields: string[];
  format_rules: Record<string, FormatRule>;
  consistency_scores: Record<string, number>;
}

// Organization rules types
export interface ClassificationCriteria {
  directory_structure: boolean;
  metadata_genre: boolean;
  filename_patterns: boolean;
}

export interface GenreClassification {
  primary_genres: string[];
  subgenre_patterns: Record<string, string[]>;
  classification_criteria: ClassificationCriteria;
}

export interface ValidationRules {
  required_metadata: string[];
  forbidden_characters: string[];
  max_path_length: number;
  unicode_support: boolean;
}

export interface OrganizationRulesSchema {
  genre_classification: GenreClassification;
  path_templates: string[];
  validation_rules: ValidationRules;
}

// Main schema type
export interface MusicSchema {
  schema_version: string;
  generated_at: string;
  root_path: string;
  scan_configuration: ScanConfiguration;
  directory_structure: DirectoryStructureSchema;
  file_classification: FileClassificationSchema;
  metadata_analysis: MetadataAnalysisSchema;
  organization_rules: OrganizationRulesSchema;
}
