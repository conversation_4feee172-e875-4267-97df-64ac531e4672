// API request/response types
export interface SelectDirectoryResponse {
  path: string;
}

export interface ScanDirectoryRequest {
  path: string;
  filters?: FilterOptions;
}

export interface ScanDirectoryResponse {
  root_path: string;
  total_files: number;
  total_audio_files: number;
  directory_structure: DirectoryInfo;
  audio_formats: Record<string, number>;
  scan_duration_ms: number;
}

// Directory information from backend
export interface DirectoryInfo {
  name: string;
  path: string;
  is_directory: boolean;
  size: number;
  children: DirectoryInfo[];
  file_count: number;
  audio_file_count: number;
}

// Filter configuration types
export interface FilterOptions {
  include_extensions?: string[];
  exclude_extensions?: string[];
  include_patterns?: string[];
  exclude_patterns?: string[];
  max_depth?: number;
  min_file_size?: number;
  max_file_size?: number;
  regex_patterns?: RegexFilter[];
}

export interface RegexFilter {
  pattern: string;
  flags?: string;
  description?: string;
  enabled: boolean;
}

// Export configuration types
export interface ExportOptions {
  format: 'json' | 'yaml';
  include_metadata?: boolean;
  include_file_list?: boolean;
  compress?: boolean;
  filename?: string;
}

// Progress tracking types
export interface ScanProgress {
  current_file: string;
  files_processed: number;
  total_files: number;
  percentage: number;
  estimated_time_remaining?: number;
}

// Error types
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Validation types
export interface ValidationResult {
  is_valid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning';
}

export interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}
