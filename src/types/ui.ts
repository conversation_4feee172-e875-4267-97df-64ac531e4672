// UI state types
export type ViewType = 'directory' | 'filters' | 'results' | 'schema';

export interface NavigationTab {
  id: ViewType;
  label: string;
  icon?: React.ComponentType;
  disabled?: boolean;
}

// Filter UI types
export interface FilterPreset {
  id: string;
  name: string;
  description: string;
  filters: FilterConfiguration;
  created_at: string;
  is_default?: boolean;
}

export interface FilterConfiguration {
  extensions: ExtensionFilter;
  patterns: PatternFilter;
  size: SizeFilter;
  depth: DepthFilter;
  regex: RegexFilter[];
}

export interface ExtensionFilter {
  mode: 'include' | 'exclude';
  extensions: string[];
}

export interface PatternFilter {
  include_patterns: string[];
  exclude_patterns: string[];
  case_sensitive: boolean;
}

export interface SizeFilter {
  enabled: boolean;
  min_size?: number;
  max_size?: number;
  unit: 'bytes' | 'kb' | 'mb' | 'gb';
}

export interface DepthFilter {
  enabled: boolean;
  max_depth: number;
}

export interface RegexFilter {
  id: string;
  pattern: string;
  flags: string;
  description: string;
  enabled: boolean;
  valid: boolean;
  error_message?: string;
}

// Tree view types
export interface TreeNode {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'directory';
  size: number;
  children?: TreeNode[];
  expanded?: boolean;
  selected?: boolean;
  file_count: number;
  audio_file_count: number;
  metadata?: {
    audio_formats: Record<string, number>;
    total_duration?: number;
    avg_bitrate?: number;
  };
}

// Results display types
export interface ResultsView {
  mode: 'tree' | 'list' | 'grid';
  sort_by: 'name' | 'size' | 'type' | 'audio_count';
  sort_order: 'asc' | 'desc';
  group_by?: 'format' | 'genre' | 'artist' | 'none';
  show_metadata: boolean;
}

// Schema export types
export interface SchemaExportSettings {
  format: 'json' | 'yaml';
  pretty_print: boolean;
  include_examples: boolean;
  include_statistics: boolean;
  include_validation_rules: boolean;
  custom_filename?: string;
}

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  variant?: 'text' | 'outlined' | 'contained';
}

// Theme types
export interface ThemeSettings {
  mode: 'light' | 'dark' | 'auto';
  primary_color: string;
  secondary_color: string;
  font_size: 'small' | 'medium' | 'large';
  compact_mode: boolean;
}

// Keyboard shortcut types
export interface KeyboardShortcut {
  key: string;
  modifiers: ('ctrl' | 'alt' | 'shift' | 'meta')[];
  action: string;
  description: string;
  global?: boolean;
}
