import { useState } from 'react'
import { invoke } from '@tauri-apps/api/core'
import './App.css'

function App() {
  const [selectedPath, setSelectedPath] = useState<string>('')
  const [isScanning, setIsScanning] = useState(false)
  const [scanResults, setScanResults] = useState<any>(null)

  const selectDirectory = async () => {
    try {
      const selected = await invoke('select_directory')
      setSelectedPath(selected as string)
    } catch (error) {
      console.error('Error selecting directory:', error)
    }
  }

  const scanDirectory = async () => {
    if (!selectedPath) return

    setIsScanning(true)
    try {
      const results = await invoke('scan_directory', { path: selectedPath })
      setScanResults(results)
    } catch (error) {
      console.error('Error scanning directory:', error)
    } finally {
      setIsScanning(false)
    }
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>Directory Schema Generator</h1>
        <p>Generate comprehensive schemas for music file organization</p>
      </header>

      <main className="app-main">
        <div className="directory-selection">
          <h2>Select Directory</h2>
          <div className="selection-controls">
            <button onClick={selectDirectory} className="select-btn">
              Select Root Directory
            </button>
            {selectedPath && (
              <div className="selected-path">
                <strong>Selected:</strong> {selectedPath}
              </div>
            )}
          </div>
        </div>

        {selectedPath && (
          <div className="scan-section">
            <h2>Directory Analysis</h2>
            <button
              onClick={scanDirectory}
              disabled={isScanning}
              className="scan-btn"
            >
              {isScanning ? 'Scanning...' : 'Analyze Directory'}
            </button>
          </div>
        )}

        {scanResults && (
          <div className="results-section">
            <h2>Scan Results</h2>
            <pre className="results-display">
              {JSON.stringify(scanResults, null, 2)}
            </pre>
          </div>
        )}
      </main>
    </div>
  )
}

export default App
