import React from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { theme } from './theme';
import { queryClient } from './lib/queryClient';
import { Layout } from './components/Layout';
import { DirectorySelection } from './components/DirectorySelection';
import { ErrorBoundary } from './components/ErrorBoundary';
import { useAppStore } from './store/appStore';
import { logger } from './lib/logger';

function App() {
  const { currentView } = useAppStore();

  // Initialize logging
  React.useEffect(() => {
    logger.info('Application started', {
      version: '1.0.0',
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    }, 'App');
  }, []);

  const renderCurrentView = () => {
    switch (currentView) {
      case 'directory':
        return <DirectorySelection />;
      case 'filters':
        return <div>Filters view coming soon...</div>;
      case 'results':
        return <div>Results view coming soon...</div>;
      case 'schema':
        return <div>Schema export view coming soon...</div>;
      default:
        return <DirectorySelection />;
    }
  };

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider theme={theme}>
          <CssBaseline />
          <Layout>
            {renderCurrentView()}
          </Layout>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
