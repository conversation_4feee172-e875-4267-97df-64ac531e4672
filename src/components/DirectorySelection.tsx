import React from 'react';
import {
  <PERSON>,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Box,
  Chip,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  PlayArrow,
  CheckCircle,
} from '@mui/icons-material';
import { useMutation } from '@tanstack/react-query';
import { useAppStore } from '../store/appStore';
import { api } from '../lib/api';

export const DirectorySelection: React.FC = () => {
  const {
    selectedPath,
    setSelectedPath,
    isScanning,
    setIsScanning,
    setScanResults,
    setError,
    setCurrentView,
  } = useAppStore();

  const selectDirectoryMutation = useMutation({
    mutationFn: api.selectDirectory,
    onSuccess: (path) => {
      setSelectedPath(path);
      setError(null);
    },
    onError: (error) => {
      setError(error.message);
    },
  });

  const scanDirectoryMutation = useMutation({
    mutationFn: api.scanDirectory,
    onMutate: () => {
      setIsScanning(true);
      setError(null);
    },
    onSuccess: (results) => {
      setScanResults(results);
      setCurrentView('results');
    },
    onError: (error) => {
      setError(error.message);
    },
    onSettled: () => {
      setIsScanning(false);
    },
  });

  const handleSelectDirectory = () => {
    selectDirectoryMutation.mutate();
  };

  const handleScanDirectory = () => {
    if (selectedPath) {
      scanDirectoryMutation.mutate({ path: selectedPath });
    }
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto' }}>
      <Card>
        <CardContent sx={{ p: 4 }}>
          <Typography variant="h4" gutterBottom sx={{ mb: 3 }}>
            Select Music Directory
          </Typography>

          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            Choose the root directory of your music library to analyze its structure
            and generate a comprehensive organization schema.
          </Typography>

          <Box sx={{ mb: 4 }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<FolderOpen />}
              onClick={handleSelectDirectory}
              disabled={selectDirectoryMutation.isPending}
              sx={{ mr: 2 }}
            >
              {selectDirectoryMutation.isPending ? (
                <>
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  Selecting...
                </>
              ) : (
                'Select Root Directory'
              )}
            </Button>

            {selectedPath && (
              <Button
                variant="contained"
                color="secondary"
                size="large"
                startIcon={<PlayArrow />}
                onClick={handleScanDirectory}
                disabled={isScanning}
              >
                {isScanning ? (
                  <>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    Analyzing...
                  </>
                ) : (
                  'Analyze Directory'
                )}
              </Button>
            )}
          </Box>

          {selectedPath && (
            <Alert severity="success" sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CheckCircle fontSize="small" />
                <Typography variant="body2">
                  <strong>Selected Directory:</strong>
                </Typography>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  mt: 1,
                  fontFamily: 'monospace',
                  wordBreak: 'break-all',
                  bgcolor: 'rgba(0,0,0,0.05)',
                  p: 1,
                  borderRadius: 1,
                }}
              >
                {selectedPath}
              </Typography>
            </Alert>
          )}

          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Chip label="Supports MP3, FLAC, WAV, M4A" variant="outlined" />
            <Chip label="Recursive directory scanning" variant="outlined" />
            <Chip label="Metadata extraction" variant="outlined" />
            <Chip label="Pattern detection" variant="outlined" />
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};
