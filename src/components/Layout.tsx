import React from 'react';
import {
  AppB<PERSON>,
  Toolbar,
  Typography,
  Container,
  Box,
  Tabs,
  Tab,
  Alert,
  Snackbar,
} from '@mui/material';
import { MusicNote } from '@mui/icons-material';
import { useAppStore } from '../store/appStore';

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { currentView, setCurrentView, error, setError } = useAppStore();

  const handleTabChange = (_: React.SyntheticEvent, newValue: string) => {
    setCurrentView(newValue as any);
  };

  const handleCloseError = () => {
    setError(null);
  };

  return (
    <Box sx={{ flexGrow: 1, minHeight: '100vh', bgcolor: 'background.default' }}>
      <AppBar position="static" elevation={0}>
        <Toolbar>
          <MusicNote sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Directory Schema Generator
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.8 }}>
            Music File Organization Tool
          </Typography>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ mt: 3, mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs
            value={currentView}
            onChange={handleTabChange}
            aria-label="navigation tabs"
          >
            <Tab label="Directory Selection" value="directory" />
            <Tab label="Filters" value="filters" />
            <Tab label="Scan Results" value="results" />
            <Tab label="Schema Export" value="schema" />
          </Tabs>
        </Box>

        {children}
      </Container>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseError} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};
