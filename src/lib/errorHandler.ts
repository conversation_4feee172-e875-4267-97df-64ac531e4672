import React from 'react';

// Error handling utilities
export class AppError extends Error {
  public readonly code: string;
  public readonly severity: 'low' | 'medium' | 'high' | 'critical';
  public readonly context?: Record<string, any>;
  public readonly timestamp: Date;

  constructor(
    message: string,
    code: string = 'UNKNOWN_ERROR',
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    context?: Record<string, any>
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.severity = severity;
    this.context = context;
    this.timestamp = new Date();
  }
}

// Specific error types
export class DirectoryError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'DIRECTORY_ERROR', 'medium', context);
    this.name = 'DirectoryError';
  }
}

export class ScanError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'SCAN_ERROR', 'medium', context);
    this.name = 'ScanError';
  }
}

export class ValidationError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'VALIDATION_ERROR', 'low', context);
    this.name = 'ValidationError';
  }
}

export class ExportError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'EXPORT_ERROR', 'medium', context);
    this.name = 'ExportError';
  }
}

// Error handler utility
export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorLog: AppError[] = [];

  private constructor() { }

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  public handleError(error: Error | AppError, context?: Record<string, any>): AppError {
    let appError: AppError;

    if (error instanceof AppError) {
      appError = error;
    } else {
      // Convert regular errors to AppError
      appError = new AppError(
        error.message,
        'UNKNOWN_ERROR',
        'medium',
        { ...context, originalError: error.name }
      );
    }

    // Log the error
    this.logError(appError);

    // Report to external service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportError(appError);
    }

    return appError;
  }

  private logError(error: AppError): void {
    this.errorLog.push(error);

    // Keep only last 100 errors to prevent memory leaks
    if (this.errorLog.length > 100) {
      this.errorLog = this.errorLog.slice(-100);
    }

    // Console logging with appropriate level
    const logLevel = this.getLogLevel(error.severity);
    console[logLevel](`[${error.code}] ${error.message}`, {
      severity: error.severity,
      context: error.context,
      timestamp: error.timestamp,
    });
  }

  private getLogLevel(severity: string): 'log' | 'warn' | 'error' {
    switch (severity) {
      case 'low':
        return 'log';
      case 'medium':
        return 'warn';
      case 'high':
      case 'critical':
        return 'error';
      default:
        return 'log';
    }
  }

  private reportError(error: AppError): void {
    // In a real application, this would send to an error reporting service
    // like Sentry, Bugsnag, etc.
    console.warn('Error reporting not implemented:', error);
  }

  public getErrorLog(): AppError[] {
    return [...this.errorLog];
  }

  public clearErrorLog(): void {
    this.errorLog = [];
  }
}

// Utility functions
export const errorHandler = ErrorHandler.getInstance();

export function handleAsyncError<T>(
  promise: Promise<T>,
  context?: Record<string, any>
): Promise<[AppError | null, T | null]> {
  return promise
    .then<[null, T]>((data: T) => [null, data])
    .catch<[AppError, null]>((error: Error) => [
      errorHandler.handleError(error, context),
      null,
    ]);
}

export function createErrorBoundary(
  fallback: (error: AppError) => React.ReactElement
) {
  return class ErrorBoundary extends React.Component<
    { children: React.ReactNode },
    { error: AppError | null }
  > {
    constructor(props: { children: React.ReactNode }) {
      super(props);
      this.state = { error: null };
    }

    static getDerivedStateFromError(error: Error): { error: AppError } {
      return { error: errorHandler.handleError(error) };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
      errorHandler.handleError(error, { errorInfo });
    }

    render() {
      if (this.state.error) {
        return fallback(this.state.error);
      }

      return this.props.children;
    }
  };
}
