// Logging utility
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  context?: Record<string, any>;
  source?: string;
}

export class Logger {
  private static instance: Logger;
  private logs: LogEntry[] = [];
  private currentLevel: LogLevel = LogLevel.INFO;
  private maxLogs: number = 1000;

  private constructor() {
    // Set log level based on environment
    if (process.env.NODE_ENV === 'development') {
      this.currentLevel = LogLevel.DEBUG;
    } else if (process.env.NODE_ENV === 'production') {
      this.currentLevel = LogLevel.WARN;
    }
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  public setLevel(level: LogLevel): void {
    this.currentLevel = level;
  }

  public setMaxLogs(max: number): void {
    this.maxLogs = max;
    this.trimLogs();
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.currentLevel;
  }

  private addLog(level: LogLevel, message: string, context?: Record<string, any>, source?: string): void {
    if (!this.shouldLog(level)) {
      return;
    }

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      context,
      source,
    };

    this.logs.push(entry);
    this.trimLogs();

    // Console output
    this.outputToConsole(entry);
  }

  private trimLogs(): void {
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
  }

  private outputToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString();
    const source = entry.source ? `[${entry.source}]` : '';
    const prefix = `${timestamp} ${source}`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(`${prefix} DEBUG:`, entry.message, entry.context);
        break;
      case LogLevel.INFO:
        console.info(`${prefix} INFO:`, entry.message, entry.context);
        break;
      case LogLevel.WARN:
        console.warn(`${prefix} WARN:`, entry.message, entry.context);
        break;
      case LogLevel.ERROR:
        console.error(`${prefix} ERROR:`, entry.message, entry.context);
        break;
    }
  }

  public debug(message: string, context?: Record<string, any>, source?: string): void {
    this.addLog(LogLevel.DEBUG, message, context, source);
  }

  public info(message: string, context?: Record<string, any>, source?: string): void {
    this.addLog(LogLevel.INFO, message, context, source);
  }

  public warn(message: string, context?: Record<string, any>, source?: string): void {
    this.addLog(LogLevel.WARN, message, context, source);
  }

  public error(message: string, context?: Record<string, any>, source?: string): void {
    this.addLog(LogLevel.ERROR, message, context, source);
  }

  public getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.logs.filter(log => log.level >= level);
    }
    return [...this.logs];
  }

  public clearLogs(): void {
    this.logs = [];
  }

  public exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }
}

// Create singleton instance
export const logger = Logger.getInstance();

// Convenience functions
export const log = {
  debug: (message: string, context?: Record<string, any>, source?: string) => 
    logger.debug(message, context, source),
  info: (message: string, context?: Record<string, any>, source?: string) => 
    logger.info(message, context, source),
  warn: (message: string, context?: Record<string, any>, source?: string) => 
    logger.warn(message, context, source),
  error: (message: string, context?: Record<string, any>, source?: string) => 
    logger.error(message, context, source),
};

// Performance logging utilities
export class PerformanceLogger {
  private static timers: Map<string, number> = new Map();

  public static start(label: string): void {
    this.timers.set(label, performance.now());
    logger.debug(`Performance timer started: ${label}`);
  }

  public static end(label: string): number {
    const startTime = this.timers.get(label);
    if (!startTime) {
      logger.warn(`Performance timer not found: ${label}`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(label);
    
    logger.info(`Performance timer ended: ${label}`, { duration: `${duration.toFixed(2)}ms` });
    return duration;
  }

  public static measure<T>(label: string, fn: () => T): T;
  public static measure<T>(label: string, fn: () => Promise<T>): Promise<T>;
  public static measure<T>(label: string, fn: () => T | Promise<T>): T | Promise<T> {
    this.start(label);
    
    try {
      const result = fn();
      
      if (result instanceof Promise) {
        return result.finally(() => this.end(label));
      } else {
        this.end(label);
        return result;
      }
    } catch (error) {
      this.end(label);
      throw error;
    }
  }
}

export const perf = PerformanceLogger;
