import { invoke } from '@tauri-apps/api/core';
import {
  ScanDirectoryResponse,
  ScanDirectoryRequest,
  FilterOptions,
  MusicSchema,
  ExportOptions
} from '../types';
import { DirectoryError, ScanError, ExportError } from './errorHandler';
import { logger, perf } from './logger';

export const api = {
  async selectDirectory(): Promise<string> {
    return perf.measure('selectDirectory', async () => {
      try {
        logger.info('Selecting directory', {}, 'API');
        const result = await invoke<string>('select_directory');
        logger.info('Directory selected successfully', { path: result }, 'API');
        return result;
      } catch (error) {
        logger.error('Failed to select directory', { error }, 'API');
        throw new DirectoryError(`Failed to select directory: ${error}`, { originalError: error });
      }
    });
  },

  async scanDirectory(request: ScanDirectoryRequest): Promise<ScanDirectoryResponse> {
    return perf.measure('scanDirectory', async () => {
      try {
        logger.info('Starting directory scan', { path: request.path }, 'API');
        const result = await invoke<ScanDirectoryResponse>('scan_directory', request);
        logger.info('Directory scan completed', {
          totalFiles: result.total_files,
          audioFiles: result.total_audio_files,
          duration: result.scan_duration_ms
        }, 'API');
        return result;
      } catch (error) {
        logger.error('Failed to scan directory', { error, request }, 'API');
        throw new ScanError(`Failed to scan directory: ${error}`, {
          originalError: error,
          request
        });
      }
    });
  },

  async generateSchema(scanResults: ScanDirectoryResponse): Promise<MusicSchema> {
    return perf.measure('generateSchema', async () => {
      try {
        logger.info('Generating schema', { rootPath: scanResults.root_path }, 'API');
        const result = await invoke<MusicSchema>('generate_schema', { scanResults });
        logger.info('Schema generated successfully', { version: result.schema_version }, 'API');
        return result;
      } catch (error) {
        logger.error('Failed to generate schema', { error, scanResults }, 'API');
        throw new Error(`Failed to generate schema: ${error}`);
      }
    });
  },

  async exportSchema(schema: MusicSchema, options: ExportOptions): Promise<string> {
    return perf.measure('exportSchema', async () => {
      try {
        logger.info('Exporting schema', { format: options.format }, 'API');
        const result = await invoke<string>('export_schema', { schema, options });
        logger.info('Schema exported successfully', { format: options.format }, 'API');
        return result;
      } catch (error) {
        logger.error('Failed to export schema', { error, options }, 'API');
        throw new ExportError(`Failed to export schema: ${error}`, {
          originalError: error,
          options
        });
      }
    });
  },
};
