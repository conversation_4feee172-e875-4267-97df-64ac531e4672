.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', sans-serif;
}

.app-header {
  text-align: center;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 300;
}

.app-header p {
  margin: 0;
  opacity: 0.8;
  font-size: 1.1rem;
}

.app-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.directory-selection,
.scan-section,
.results-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.directory-selection h2,
.scan-section h2,
.results-section h2 {
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 400;
}

.selection-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.select-btn,
.scan-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.select-btn:hover,
.scan-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.select-btn:disabled,
.scan-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.selected-path {
  background: rgba(0, 0, 0, 0.2);
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #4CAF50;
  word-break: break-all;
}

.results-display {
  background: rgba(0, 0, 0, 0.3);
  padding: 1.5rem;
  border-radius: 8px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  max-height: 400px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .app-main {
    padding: 1rem;
  }
  
  .directory-selection,
  .scan-section,
  .results-section {
    padding: 1.5rem;
  }
  
  .app-header h1 {
    font-size: 2rem;
  }
}
