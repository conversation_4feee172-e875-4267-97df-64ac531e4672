{"name": "prepwerk", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "keywords": ["music", "directory", "schema", "organization", "tauri"], "author": "", "license": "ISC", "description": "Directory Schema Generator for Music File Organization", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@modelcontextprotocol/server-filesystem": "^2025.3.28", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@tanstack/react-query": "^5.81.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "zustand": "^5.0.6"}, "devDependencies": {"@tauri-apps/api": "^2.6.0", "@tauri-apps/cli": "^2.6.1", "@vitejs/plugin-react": "^4.6.0", "typescript": "^5.8.3", "vite": "^5.4.19"}}