[package]
name = "prepwerk"
version = "0.1.0"
description = "Directory Schema Generator for Music File Organization"
authors = ["<PERSON>"]
license = ""
repository = ""
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.3.0" }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
log = "0.4"
tauri = { version = "2.6.1", features = ["dialog"] }
tauri-plugin-log = "2"
tauri-plugin-dialog = "2"
walkdir = "2.3"
lofty = "0.21"
regex = "1.7"
tokio = { version = "1.0", features = ["full"] }
uuid = { version = "1.0", features = ["v4"] }
serde_yaml = "0.9"
chrono = { version = "0.4", features = ["serde"] }
