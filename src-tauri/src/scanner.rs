use lofty::{AudioFile, Probe};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;

#[derive(Debug, Serialize, Deserialize)]
pub struct AudioMetadata {
    pub title: Option<String>,
    pub artist: Option<String>,
    pub album: Option<String>,
    pub genre: Option<String>,
    pub year: Option<u32>,
    pub track: Option<u32>,
    pub duration: Option<u64>, // in seconds
    pub bitrate: Option<u32>,
    pub sample_rate: Option<u32>,
    pub format: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AudioFileInfo {
    pub path: String,
    pub filename: String,
    pub size: u64,
    pub metadata: Option<AudioMetadata>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PatternAnalysis {
    pub filename_patterns: Vec<String>,
    pub directory_patterns: Vec<String>,
    pub metadata_consistency: HashMap<String, f64>, // field -> consistency score (0.0 to 1.0)
    pub genre_distribution: HashMap<String, usize>,
    pub common_naming_conventions: Vec<String>,
}

pub fn extract_audio_metadata(file_path: &Path) -> Option<AudioMetadata> {
    if let Ok(tagged_file) = Probe::open(file_path)
        .and_then(|probe| probe.read())
    {
        let properties = tagged_file.properties();
        let tag = tagged_file.primary_tag().or_else(|| tagged_file.first_tag());

        let metadata = AudioMetadata {
            title: tag.and_then(|t| t.title().map(|s| s.to_string())),
            artist: tag.and_then(|t| t.artist().map(|s| s.to_string())),
            album: tag.and_then(|t| t.album().map(|s| s.to_string())),
            genre: tag.and_then(|t| t.genre().map(|s| s.to_string())),
            year: tag.and_then(|t| t.year()),
            track: tag.and_then(|t| t.track()),
            duration: properties.duration().map(|d| d.as_secs()),
            bitrate: properties.audio_bitrate(),
            sample_rate: properties.sample_rate(),
            format: file_path
                .extension()
                .unwrap_or_default()
                .to_string_lossy()
                .to_uppercase(),
        };

        Some(metadata)
    } else {
        None
    }
}

pub fn analyze_filename_patterns(audio_files: &[AudioFileInfo]) -> Vec<String> {
    let mut patterns = Vec::new();
    let mut pattern_counts: HashMap<String, usize> = HashMap::new();

    for file in audio_files {
        let filename = &file.filename;
        
        // Remove extension
        let name_without_ext = filename.rsplit('.').nth(1).unwrap_or(filename);
        
        // Common patterns to detect
        if name_without_ext.contains(" - ") {
            if name_without_ext.matches(" - ").count() == 1 {
                patterns.push("Artist - Title".to_string());
                *pattern_counts.entry("Artist - Title".to_string()).or_insert(0) += 1;
            } else if name_without_ext.matches(" - ").count() == 2 {
                patterns.push("Artist - Album - Title".to_string());
                *pattern_counts.entry("Artist - Album - Title".to_string()).or_insert(0) += 1;
            }
        }
        
        // Check for track number patterns
        if regex::Regex::new(r"^\d{1,2}\.?\s").unwrap().is_match(name_without_ext) {
            patterns.push("Track# Title".to_string());
            *pattern_counts.entry("Track# Title".to_string()).or_insert(0) += 1;
        }
        
        if regex::Regex::new(r"^\d{1,2}\s-\s").unwrap().is_match(name_without_ext) {
            patterns.push("Track# - Title".to_string());
            *pattern_counts.entry("Track# - Title".to_string()).or_insert(0) += 1;
        }
    }

    // Return patterns sorted by frequency
    let mut sorted_patterns: Vec<(String, usize)> = pattern_counts.into_iter().collect();
    sorted_patterns.sort_by(|a, b| b.1.cmp(&a.1));
    
    sorted_patterns.into_iter().map(|(pattern, _)| pattern).collect()
}

pub fn analyze_metadata_consistency(audio_files: &[AudioFileInfo]) -> HashMap<String, f64> {
    let mut consistency = HashMap::new();
    let total_files = audio_files.len() as f64;
    
    if total_files == 0.0 {
        return consistency;
    }

    let fields = vec!["title", "artist", "album", "genre", "year", "track"];
    
    for field in fields {
        let mut filled_count = 0;
        
        for file in audio_files {
            if let Some(ref metadata) = file.metadata {
                let is_filled = match field {
                    "title" => metadata.title.is_some(),
                    "artist" => metadata.artist.is_some(),
                    "album" => metadata.album.is_some(),
                    "genre" => metadata.genre.is_some(),
                    "year" => metadata.year.is_some(),
                    "track" => metadata.track.is_some(),
                    _ => false,
                };
                
                if is_filled {
                    filled_count += 1;
                }
            }
        }
        
        consistency.insert(field.to_string(), filled_count as f64 / total_files);
    }
    
    consistency
}

pub fn analyze_genre_distribution(audio_files: &[AudioFileInfo]) -> HashMap<String, usize> {
    let mut distribution = HashMap::new();
    
    for file in audio_files {
        if let Some(ref metadata) = file.metadata {
            if let Some(ref genre) = metadata.genre {
                *distribution.entry(genre.clone()).or_insert(0) += 1;
            }
        }
    }
    
    distribution
}

pub fn detect_directory_patterns(directory_paths: &[String]) -> Vec<String> {
    let mut patterns = Vec::new();
    
    // Analyze common directory structures
    for path in directory_paths {
        let parts: Vec<&str> = path.split('/').filter(|s| !s.is_empty()).collect();
        
        if parts.len() >= 3 {
            // Check for Genre/Artist/Album pattern
            patterns.push("Genre/Artist/Album".to_string());
        }
        
        if parts.len() >= 2 {
            // Check for Artist/Album pattern
            patterns.push("Artist/Album".to_string());
        }
    }
    
    // Remove duplicates and return
    patterns.sort();
    patterns.dedup();
    patterns
}
