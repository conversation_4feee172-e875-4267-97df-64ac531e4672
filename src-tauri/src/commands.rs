use crate::{<PERSON><PERSON>n<PERSON>, ScanResult};
use std::collections::HashMap;
use std::path::PathBuf;
use std::time::Instant;
use tauri::command;
use tauri_plugin_dialog::{DialogExt, MessageDialogKind};

#[command]
pub async fn select_directory(app: tauri::AppHandle) -> Result<String, String> {
    let file_path = app
        .dialog()
        .file()
        .set_title("Select Music Directory")
        .pick_folder()
        .await;

    match file_path {
        Some(path) => Ok(path.to_string_lossy().to_string()),
        None => Err("No directory selected".to_string()),
    }
}

#[command]
pub async fn scan_directory(path: String) -> Result<ScanResult, String> {
    let start_time = Instant::now();
    let path_buf = PathBuf::from(&path);
    
    if !path_buf.exists() {
        return Err("Directory does not exist".to_string());
    }

    if !path_buf.is_dir() {
        return Err("Path is not a directory".to_string());
    }

    let mut total_files = 0;
    let mut total_audio_files = 0;
    let mut audio_formats: HashMap<String, usize> = HashMap::new();

    // Audio file extensions to look for
    let audio_extensions = vec![
        "mp3", "flac", "wav", "m4a", "aac", "ogg", "wma", "aiff", "ape", "opus"
    ];

    let directory_structure = scan_directory_recursive(&path_buf, &audio_extensions, &mut total_files, &mut total_audio_files, &mut audio_formats)?;

    let scan_duration = start_time.elapsed();

    Ok(ScanResult {
        root_path: path,
        total_files,
        total_audio_files,
        directory_structure,
        audio_formats,
        scan_duration_ms: scan_duration.as_millis() as u64,
    })
}

fn scan_directory_recursive(
    path: &PathBuf,
    audio_extensions: &[&str],
    total_files: &mut usize,
    total_audio_files: &mut usize,
    audio_formats: &mut HashMap<String, usize>,
) -> Result<DirectoryInfo, String> {
    let mut children = Vec::new();
    let mut file_count = 0;
    let mut audio_file_count = 0;

    if let Ok(entries) = std::fs::read_dir(path) {
        for entry in entries {
            if let Ok(entry) = entry {
                let entry_path = entry.path();
                let file_name = entry_path
                    .file_name()
                    .unwrap_or_default()
                    .to_string_lossy()
                    .to_string();

                // Skip hidden files and directories
                if file_name.starts_with('.') {
                    continue;
                }

                if entry_path.is_dir() {
                    // Recursively scan subdirectory
                    match scan_directory_recursive(&entry_path, audio_extensions, total_files, total_audio_files, audio_formats) {
                        Ok(child_info) => {
                            file_count += child_info.file_count;
                            audio_file_count += child_info.audio_file_count;
                            children.push(child_info);
                        }
                        Err(_) => {
                            // Skip directories we can't read
                            continue;
                        }
                    }
                } else {
                    // Process file
                    *total_files += 1;
                    file_count += 1;

                    // Check if it's an audio file
                    if let Some(extension) = entry_path.extension() {
                        let ext = extension.to_string_lossy().to_lowercase();
                        if audio_extensions.contains(&ext.as_str()) {
                            *total_audio_files += 1;
                            audio_file_count += 1;
                            
                            // Count audio format
                            *audio_formats.entry(ext).or_insert(0) += 1;
                        }
                    }

                    // Add file info
                    let file_size = entry.metadata().map(|m| m.len()).unwrap_or(0);
                    children.push(DirectoryInfo {
                        name: file_name,
                        path: entry_path.to_string_lossy().to_string(),
                        is_directory: false,
                        size: file_size,
                        children: Vec::new(),
                        file_count: 1,
                        audio_file_count: if audio_extensions.iter().any(|&ext| {
                            entry_path.extension()
                                .map(|e| e.to_string_lossy().to_lowercase() == ext)
                                .unwrap_or(false)
                        }) { 1 } else { 0 },
                    });
                }
            }
        }
    }

    // Sort children: directories first, then files, both alphabetically
    children.sort_by(|a, b| {
        match (a.is_directory, b.is_directory) {
            (true, false) => std::cmp::Ordering::Less,
            (false, true) => std::cmp::Ordering::Greater,
            _ => a.name.cmp(&b.name),
        }
    });

    Ok(DirectoryInfo {
        name: path
            .file_name()
            .unwrap_or_default()
            .to_string_lossy()
            .to_string(),
        path: path.to_string_lossy().to_string(),
        is_directory: true,
        size: 0, // Directory size calculation can be added later if needed
        children,
        file_count,
        audio_file_count,
    })
}
