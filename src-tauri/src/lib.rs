use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use tauri::Manager;
use walkdir::WalkDir;

mod commands;
mod scanner;
mod schema;

use commands::*;

#[derive(Debug, Serialize, Deserialize)]
pub struct DirectoryInfo {
    pub name: String,
    pub path: String,
    pub is_directory: bool,
    pub size: u64,
    pub children: Vec<DirectoryInfo>,
    pub file_count: usize,
    pub audio_file_count: usize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ScanResult {
    pub root_path: String,
    pub total_files: usize,
    pub total_audio_files: usize,
    pub directory_structure: DirectoryInfo,
    pub audio_formats: HashMap<String, usize>,
    pub scan_duration_ms: u64,
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_dialog::init())
        .setup(|app| {
            if cfg!(debug_assertions) {
                app.handle().plugin(
                    tauri_plugin_log::Builder::default()
                        .level(log::LevelFilter::Info)
                        .build(),
                )?;
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![select_directory, scan_directory])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
