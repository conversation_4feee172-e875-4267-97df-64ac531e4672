use crate::scanner::{AudioFileInfo, PatternAnalysis};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Serialize, Deserialize)]
pub struct MusicSchema {
    pub schema_version: String,
    pub generated_at: String,
    pub root_path: String,
    pub scan_configuration: ScanConfiguration,
    pub directory_structure: DirectoryStructureSchema,
    pub file_classification: FileClassificationSchema,
    pub metadata_analysis: MetadataAnalysisSchema,
    pub organization_rules: OrganizationRulesSchema,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ScanConfiguration {
    pub filters_applied: Vec<String>,
    pub scan_depth: Option<u32>,
    pub included_extensions: Vec<String>,
    pub excluded_paths: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DirectoryStructureSchema {
    pub hierarchy: DirectoryHierarchy,
    pub organization_patterns: OrganizationPatterns,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DirectoryHierarchy {
    pub name: String,
    pub path: String,
    pub children: Vec<DirectoryHierarchy>,
    pub file_count: usize,
    pub audio_file_count: usize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrganizationPatterns {
    pub genre_based_organization: bool,
    pub artist_album_structure: bool,
    pub year_based_subdivision: bool,
    pub common_patterns: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FileClassificationSchema {
    pub audio_formats: HashMap<String, AudioFormatInfo>,
    pub naming_conventions: NamingConventions,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AudioFormatInfo {
    pub count: usize,
    pub avg_bitrate: Option<f64>,
    pub avg_sample_rate: Option<f64>,
    pub total_size_mb: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NamingConventions {
    pub patterns: Vec<String>,
    pub consistency: f64,
    pub common_separators: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MetadataAnalysisSchema {
    pub required_fields: Vec<String>,
    pub optional_fields: Vec<String>,
    pub empty_fields: Vec<String>,
    pub format_rules: HashMap<String, FormatRule>,
    pub consistency_scores: HashMap<String, f64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FormatRule {
    pub pattern: String,
    pub examples: Vec<String>,
    pub description: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrganizationRulesSchema {
    pub genre_classification: GenreClassification,
    pub path_templates: Vec<String>,
    pub validation_rules: ValidationRules,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GenreClassification {
    pub primary_genres: Vec<String>,
    pub subgenre_patterns: HashMap<String, Vec<String>>,
    pub classification_criteria: ClassificationCriteria,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ClassificationCriteria {
    pub directory_structure: bool,
    pub metadata_genre: bool,
    pub filename_patterns: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ValidationRules {
    pub required_metadata: Vec<String>,
    pub forbidden_characters: Vec<String>,
    pub max_path_length: u32,
    pub unicode_support: bool,
}

impl MusicSchema {
    pub fn new(
        root_path: String,
        audio_files: &[AudioFileInfo],
        pattern_analysis: &PatternAnalysis,
    ) -> Self {
        let now = chrono::Utc::now();
        
        // Analyze audio formats
        let mut audio_formats = HashMap::new();
        for file in audio_files {
            if let Some(ref metadata) = file.metadata {
                let format = metadata.format.to_lowercase();
                let entry = audio_formats.entry(format).or_insert(AudioFormatInfo {
                    count: 0,
                    avg_bitrate: None,
                    avg_sample_rate: None,
                    total_size_mb: 0.0,
                });
                
                entry.count += 1;
                entry.total_size_mb += file.size as f64 / (1024.0 * 1024.0);
                
                // Calculate averages
                if let Some(bitrate) = metadata.bitrate {
                    entry.avg_bitrate = Some(
                        entry.avg_bitrate.unwrap_or(0.0) + bitrate as f64
                    );
                }
                
                if let Some(sample_rate) = metadata.sample_rate {
                    entry.avg_sample_rate = Some(
                        entry.avg_sample_rate.unwrap_or(0.0) + sample_rate as f64
                    );
                }
            }
        }
        
        // Finalize averages
        for format_info in audio_formats.values_mut() {
            if let Some(ref mut avg_bitrate) = format_info.avg_bitrate {
                *avg_bitrate /= format_info.count as f64;
            }
            if let Some(ref mut avg_sample_rate) = format_info.avg_sample_rate {
                *avg_sample_rate /= format_info.count as f64;
            }
        }

        MusicSchema {
            schema_version: "1.0.0".to_string(),
            generated_at: now.to_rfc3339(),
            root_path,
            scan_configuration: ScanConfiguration {
                filters_applied: vec![],
                scan_depth: None,
                included_extensions: vec![
                    ".mp3".to_string(),
                    ".flac".to_string(),
                    ".wav".to_string(),
                    ".m4a".to_string(),
                ],
                excluded_paths: vec![],
            },
            directory_structure: DirectoryStructureSchema {
                hierarchy: DirectoryHierarchy {
                    name: "root".to_string(),
                    path: "/".to_string(),
                    children: vec![],
                    file_count: audio_files.len(),
                    audio_file_count: audio_files.len(),
                },
                organization_patterns: OrganizationPatterns {
                    genre_based_organization: true,
                    artist_album_structure: true,
                    year_based_subdivision: false,
                    common_patterns: pattern_analysis.directory_patterns.clone(),
                },
            },
            file_classification: FileClassificationSchema {
                audio_formats,
                naming_conventions: NamingConventions {
                    patterns: pattern_analysis.filename_patterns.clone(),
                    consistency: 0.85, // This would be calculated from actual analysis
                    common_separators: vec![" - ".to_string(), "_".to_string(), " ".to_string()],
                },
            },
            metadata_analysis: MetadataAnalysisSchema {
                required_fields: vec!["title".to_string(), "artist".to_string(), "album".to_string()],
                optional_fields: vec!["year".to_string(), "track".to_string(), "genre".to_string()],
                empty_fields: vec!["composer".to_string(), "conductor".to_string()],
                format_rules: HashMap::new(),
                consistency_scores: pattern_analysis.metadata_consistency.clone(),
            },
            organization_rules: OrganizationRulesSchema {
                genre_classification: GenreClassification {
                    primary_genres: pattern_analysis.genre_distribution.keys().cloned().collect(),
                    subgenre_patterns: HashMap::new(),
                    classification_criteria: ClassificationCriteria {
                        directory_structure: true,
                        metadata_genre: true,
                        filename_patterns: false,
                    },
                },
                path_templates: vec![
                    "{genre}/{artist}/{year} - {album}/{track} - {title}".to_string(),
                    "{genre}/{artist}/{album}/{track}. {title}".to_string(),
                ],
                validation_rules: ValidationRules {
                    required_metadata: vec!["title".to_string(), "artist".to_string()],
                    forbidden_characters: vec!["<".to_string(), ">".to_string(), ":".to_string()],
                    max_path_length: 260,
                    unicode_support: true,
                },
            },
        }
    }
    
    pub fn to_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string_pretty(self)
    }
    
    pub fn to_yaml(&self) -> Result<String, serde_yaml::Error> {
        serde_yaml::to_string(self)
    }
}
