{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "prepwerk", "version": "0.1.0", "identifier": "com.tauri.dev", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:5173", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "Directory Schema Generator", "width": 1200, "height": 800, "resizable": true, "fullscreen": false, "minWidth": 800, "minHeight": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}