# Directory Schema Generator for Music File Organization

## Project Overview

This application will generate comprehensive directory structure schemas specifically designed for music file organization. The schema output will serve as configuration input for future music library management applications, helping to automate music preparation workflows by teaching AI systems about:

- Directory and file organization patterns
- Sub-genre classification based on song elements
- Comment patterns in metadata tags
- Required metadata fields and formatting rules

## Technical Architecture

**Framework**: Tauri (Rust backend + React/TypeScript frontend)
- **Backend**: Rust for high-performance file system operations and audio metadata reading
- **Frontend**: React with TypeScript for rich, interactive UI
- **Export Formats**: JSON and YAML for schema output
- **Audio Processing**: Native audio metadata extraction and pattern analysis

## Core Requirements

### 1. Directory Browsing & Selection
- Native file dialog for root directory selection
- Real-time directory tree visualization
- Support for large directory structures (thousands of files)
- Cross-platform compatibility (Windows, macOS, Linux)

### 2. Advanced Filtering System
- **Inclusion/Exclusion Controls**: Toggle for entire directories or file types
- **Text-based Filtering**: Contains, starts with, ends with, exact match
- **Regex Pattern Matching**: Advanced pattern filtering with validation
- **File Extension Filtering**: Support for audio formats (.mp3, .flac, .wav, .m4a, etc.)
- **Directory Depth Limiting**: Control scan depth for performance
- **File Size Filtering**: Min/max file size constraints
- **Filter Presets**: Save/load filter configurations

### 3. Audio Metadata Analysis
- **Metadata Extraction**: Read ID3, FLAC, MP4, and other audio metadata
- **Pattern Detection**: Analyze naming conventions and organizational patterns
- **Consistency Analysis**: Identify metadata field usage patterns
- **Genre Classification**: Learn sub-genre organization from directory structure
- **Comment Pattern Analysis**: Extract patterns from comment fields

### 4. Schema Generation
- **Hierarchical Structure**: Complete directory tree representation
- **File Classification**: Audio format categorization and patterns
- **Naming Conventions**: Detected filename and directory naming rules
- **Metadata Rules**: Required fields, formatting patterns, validation rules
- **Organization Templates**: Path templates for automated file organization

### 5. Export & Validation
- **JSON Export**: Machine-readable schema format
- **YAML Export**: Human-readable schema format
- **Schema Validation**: Ensure generated schemas are valid and complete
- **Preview Functionality**: Real-time preview of schema generation
- **Import/Export**: Save and load filter configurations

## Detailed Implementation Plan

### Phase 1: Project Setup & Infrastructure
**Status**: Not Started

#### 1.1 Initialize Tauri Project
- [ ] Create new Tauri project with React/TypeScript template
- [ ] Configure Cargo.toml with required dependencies
- [ ] Set up project structure and build configuration
- [ ] Configure development environment and tooling

**Dependencies**:
```toml
[dependencies]
tauri = { version = "1.0", features = ["api-all"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
walkdir = "2.3"
lofty = "0.12"
regex = "1.7"
tokio = { version = "1.0", features = ["full"] }
uuid = { version = "1.0", features = ["v4"] }
```

#### 1.2 Frontend Setup
- [ ] Install and configure React dependencies
- [ ] Set up TypeScript configuration
- [ ] Install UI component library (Material-UI or Ant Design)
- [ ] Configure state management (React Query/Zustand)
- [ ] Set up routing and basic app structure

**Frontend Dependencies**:
```json
{
  "@mui/material": "^5.0.0",
  "@mui/icons-material": "^5.0.0",
  "@emotion/react": "^11.0.0",
  "@emotion/styled": "^11.0.0",
  "react-query": "^3.39.0",
  "react-router-dom": "^6.0.0",
  "monaco-editor": "^0.34.0",
  "react-tree-graph": "^8.0.0"
}
```

#### 1.3 Core Data Structures
- [ ] Define TypeScript interfaces for schema structure
- [ ] Create Rust structs for internal data representation
- [ ] Implement serialization/deserialization between frontend and backend
- [ ] Set up error handling and logging systems

### Phase 2: Core Directory Scanning
**Status**: Not Started

#### 2.1 File System Operations
- [ ] Implement directory traversal with walkdir
- [ ] Create file information extraction (size, modified date, extension)
- [ ] Add progress tracking for large directory scans
- [ ] Implement cancellation support for long-running operations

#### 2.2 Basic Directory Tree
- [ ] Create tree data structure for directory representation
- [ ] Implement efficient tree building from file system scan
- [ ] Add tree serialization for frontend consumption
- [ ] Create tree visualization component in React

#### 2.3 Performance Optimization
- [ ] Implement async scanning with progress updates
- [ ] Add caching for previously scanned directories
- [ ] Optimize memory usage for large directory structures
- [ ] Add configurable scan limits and timeouts

### Phase 3: Filtering System Implementation
**Status**: Not Started

#### 3.1 Filter Engine
- [ ] Create filter trait/interface system
- [ ] Implement basic include/exclude filters
- [ ] Add text-based filtering (contains, starts with, ends with)
- [ ] Implement regex pattern filtering with validation
- [ ] Create file extension filtering system

#### 3.2 Filter UI Components
- [ ] Design and implement filter configuration panel
- [ ] Create regex editor with syntax highlighting
- [ ] Add file extension selector with common audio formats
- [ ] Implement filter preview with real-time updates
- [ ] Create filter preset management system

#### 3.3 Advanced Filtering
- [ ] Add directory depth limiting
- [ ] Implement file size filtering
- [ ] Create compound filter logic (AND/OR operations)
- [ ] Add filter validation and error handling
- [ ] Implement filter performance optimization

### Phase 4: Audio Metadata Analysis
**Status**: Not Started

#### 4.1 Metadata Extraction
- [ ] Integrate lofty crate for audio metadata reading
- [ ] Support major audio formats (MP3, FLAC, MP4, OGG, WAV)
- [ ] Extract standard metadata fields (title, artist, album, genre, etc.)
- [ ] Handle metadata encoding issues and edge cases

#### 4.2 Pattern Detection
- [ ] Analyze filename patterns across audio files
- [ ] Detect directory organization patterns
- [ ] Identify metadata consistency patterns
- [ ] Extract comment field patterns and structures

#### 4.3 Music-Specific Analysis
- [ ] Implement genre classification pattern detection
- [ ] Analyze sub-genre organization from directory structure
- [ ] Detect required vs optional metadata fields
- [ ] Create metadata formatting rule detection

### Phase 5: Schema Generation & Export
**Status**: Not Started

#### 5.1 Schema Structure Design
- [ ] Define comprehensive schema format for music organization
- [ ] Include directory hierarchy representation
- [ ] Add file classification and pattern rules
- [ ] Incorporate metadata requirements and formatting rules

#### 5.2 Schema Generation Engine
- [ ] Implement schema building from analyzed data
- [ ] Create rule inference from detected patterns
- [ ] Add validation for generated schemas
- [ ] Implement schema optimization and cleanup

#### 5.3 Export Functionality
- [ ] Implement JSON schema export with proper formatting
- [ ] Add YAML export for human readability
- [ ] Create schema validation before export
- [ ] Add export configuration options

### Phase 6: User Interface Implementation
**Status**: Not Started

#### 6.1 Main Application Layout
- [ ] Design responsive application layout
- [ ] Implement directory browser panel
- [ ] Create filter configuration panel
- [ ] Add schema preview panel

#### 6.2 Directory Tree Visualization
- [ ] Implement interactive directory tree component
- [ ] Add checkbox selection for include/exclude
- [ ] Create file count and size indicators
- [ ] Add tree search and navigation features

#### 6.3 Filter Management UI
- [ ] Create intuitive filter configuration interface
- [ ] Implement real-time filter preview
- [ ] Add filter preset management
- [ ] Create filter validation and error display

#### 6.4 Schema Preview & Export
- [ ] Implement live schema preview
- [ ] Create schema editing capabilities
- [ ] Add export dialog with format options
- [ ] Implement schema import functionality

### Phase 7: Testing & Validation
**Status**: Not Started

#### 7.1 Unit Testing
- [ ] Write tests for core filtering logic
- [ ] Test audio metadata extraction
- [ ] Validate schema generation accuracy
- [ ] Test file system operations

#### 7.2 Integration Testing
- [ ] Test complete workflow from directory scan to export
- [ ] Validate filter combinations and edge cases
- [ ] Test performance with large directory structures
- [ ] Verify cross-platform compatibility

#### 7.3 User Acceptance Testing
- [ ] Test with real music library structures
- [ ] Validate generated schemas with actual use cases
- [ ] Test UI usability and responsiveness
- [ ] Gather feedback and iterate on design

### Phase 8: Documentation & Deployment
**Status**: Not Started

#### 8.1 Documentation
- [ ] Create user manual and tutorials
- [ ] Document schema format specification
- [ ] Write developer documentation for schema consumption
- [ ] Create troubleshooting guide

#### 8.2 Deployment Preparation
- [ ] Configure build scripts for all platforms
- [ ] Set up code signing for distribution
- [ ] Create installer packages
- [ ] Prepare release documentation

#### 8.3 Release & Distribution
- [ ] Create GitHub releases with binaries
- [ ] Set up automatic update mechanism
- [ ] Create project website and documentation
- [ ] Prepare for user feedback and bug reports

## Schema Output Specification

The generated schema will follow this structure:

```json
{
  "schemaVersion": "1.0.0",
  "generatedAt": "2024-01-01T00:00:00Z",
  "rootPath": "/path/to/music/library",
  "scanConfiguration": {
    "filtersApplied": [...],
    "scanDepth": 10,
    "includedExtensions": [".mp3", ".flac", ".wav"],
    "excludedPaths": [...]
  },
  "directoryStructure": {
    "hierarchy": {
      "name": "root",
      "path": "/",
      "children": [...],
      "fileCount": 1000,
      "audioFileCount": 950
    },
    "organizationPatterns": {
      "genreBasedOrganization": true,
      "artistAlbumStructure": true,
      "yearBasedSubdivision": false,
      "commonPatterns": [
        "Genre/Artist/Album",
        "Genre/Subgenre/Artist/Album"
      ]
    }
  },
  "fileClassification": {
    "audioFormats": {
      "mp3": { "count": 500, "avgBitrate": 320 },
      "flac": { "count": 400, "avgBitrate": 1411 },
      "wav": { "count": 50, "avgBitrate": 1411 }
    },
    "namingConventions": {
      "patterns": [
        "{track} - {title}",
        "{artist} - {title}",
        "{track}. {artist} - {title}"
      ],
      "consistency": 0.85
    }
  },
  "metadataAnalysis": {
    "requiredFields": ["title", "artist", "album", "genre"],
    "optionalFields": ["year", "track", "albumartist"],
    "emptyFields": ["composer", "conductor"],
    "formatRules": {
      "genre": {
        "pattern": "Primary Genre / Sub-genre",
        "examples": ["Electronic / House", "Rock / Progressive"]
      },
      "comments": {
        "pattern": "Key: Value; Key2: Value2",
        "commonKeys": ["Energy", "Mood", "BPM", "Key"]
      }
    },
    "consistencyScores": {
      "title": 0.98,
      "artist": 0.95,
      "album": 0.90,
      "genre": 0.85
    }
  },
  "organizationRules": {
    "genreClassification": {
      "primaryGenres": ["Electronic", "Rock", "Jazz", "Classical"],
      "subgenrePatterns": {
        "Electronic": ["House", "Techno", "Ambient", "Drum & Bass"],
        "Rock": ["Progressive", "Alternative", "Metal", "Indie"]
      },
      "classificationCriteria": {
        "directoryStructure": true,
        "metadataGenre": true,
        "filenamePatterns": false
      }
    },
    "pathTemplates": [
      "{genre}/{artist}/{year} - {album}/{track} - {title}",
      "{genre}/{subgenre}/{artist}/{album}/{track}. {title}"
    ],
    "validationRules": {
      "requiredMetadata": ["title", "artist", "album", "genre"],
      "forbiddenCharacters": ["<", ">", ":", "\"", "|", "?", "*"],
      "maxPathLength": 260,
      "unicodeSupport": true
    }
  }
}
```

## Tasks

### Immediate Tasks (Phase 1)
- [ ] **1.1.1** Create new Tauri project with React/TypeScript template
- [ ] **1.1.2** Configure Cargo.toml with audio processing dependencies
- [ ] **1.1.3** Set up development environment and build scripts
- [ ] **1.2.1** Install and configure React with TypeScript
- [ ] **1.2.2** Set up Material-UI component library
- [ ] **1.2.3** Configure state management and routing
- [ ] **1.3.1** Define TypeScript interfaces for schema structure
- [ ] **1.3.2** Create Rust structs for data representation
- [ ] **1.3.3** Implement frontend-backend communication

### Core Development Tasks (Phase 2-3)
- [ ] **2.1.1** Implement directory traversal with progress tracking
- [ ] **2.1.2** Create file information extraction system
- [ ] **2.2.1** Build directory tree data structure
- [ ] **2.2.2** Create React tree visualization component
- [ ] **3.1.1** Implement core filtering engine
- [ ] **3.1.2** Add regex pattern filtering with validation
- [ ] **3.2.1** Design filter configuration UI
- [ ] **3.2.2** Create real-time filter preview

### Advanced Features (Phase 4-5)
- [ ] **4.1.1** Integrate audio metadata extraction
- [ ] **4.2.1** Implement pattern detection algorithms
- [ ] **4.3.1** Create music-specific analysis features
- [ ] **5.1.1** Design comprehensive schema format
- [ ] **5.2.1** Build schema generation engine
- [ ] **5.3.1** Implement JSON/YAML export functionality

### UI/UX & Testing (Phase 6-7)
- [ ] **6.1.1** Create responsive application layout
- [ ] **6.2.1** Implement interactive directory tree
- [ ] **6.3.1** Build filter management interface
- [ ] **6.4.1** Create schema preview and export UI
- [ ] **7.1.1** Write comprehensive unit tests
- [ ] **7.2.1** Perform integration testing
- [ ] **7.3.1** Conduct user acceptance testing

### Documentation & Release (Phase 8)
- [ ] **8.1.1** Create user documentation and tutorials
- [ ] **8.2.1** Configure build and deployment scripts
- [ ] **8.3.1** Prepare release packages and distribution

---

## PROJECT RULE (MUST BE FOLLOWED AT ALL TIMES)

**After completing each step, ALWAYS:**
1. Refer back to this plan document
2. Mark completed steps as "✅ Complete"
3. Mark the current step being worked on as "🔄 In Progress"
4. Update any relevant notes or discoveries in the step description
5. Ensure the next step is clearly identified before proceeding

This ensures continuous progress tracking and maintains project organization throughout development.