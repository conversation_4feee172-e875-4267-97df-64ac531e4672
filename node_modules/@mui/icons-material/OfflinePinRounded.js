"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2m4 16H8c-.55 0-1-.45-1-1s.45-1 1-1h8c.55 0 1 .45 1 1s-.45 1-1 1m-6.41-4.71L7.7 11.4a.984.984 0 0 1 0-1.4c.39-.39 1.01-.39 1.4 0l1.2 1.2 4.6-4.6c.39-.39 1.01-.39 1.4 0s.39 1.01 0 1.4l-5.29 5.29c-.39.39-1.03.39-1.42 0"
}), 'OfflinePinRounded');